name: ci

on:
  push:
    branches: [main]
    paths:
      - 'mem0/**'
      - 'tests/**'
      - 'embedchain/**'
  pull_request:
    paths:
      - 'mem0/**'
      - 'tests/**'
      - 'embedchain/**'

jobs:
  check_changes:
    runs-on: ubuntu-latest
    outputs:
      mem0_changed: ${{ steps.filter.outputs.mem0 }}
      embedchain_changed: ${{ steps.filter.outputs.embedchain }}
    steps:
    - uses: actions/checkout@v3
    - uses: dorny/paths-filter@v2
      id: filter
      with:
        filters: |
          mem0:
            - 'mem0/**'
            - 'tests/**'
          embedchain:
            - 'embedchain/**'

  build_mem0:
    needs: check_changes
    if: needs.check_changes.outputs.mem0_changed == 'true'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install Hatch
        run: pip install hatch
      - name: Load cached venv
        id: cached-hatch-dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-mem0-${{ runner.os }}-${{ hashFiles('**/pyproject.toml') }}
      - name: Install dependencies
        run: |
          make install_all
          pip install -e ".[test]"
          pip install pinecone pinecone-text
        if: steps.cached-hatch-dependencies.outputs.cache-hit != 'true'
      - name: Run Formatting
        run: |
          mkdir -p .ruff_cache && chmod -R 777 .ruff_cache
          hatch run format
      - name: Run tests and generate coverage report
        run: make test

  build_embedchain:
    needs: check_changes
    if: needs.check_changes.outputs.embedchain_changed == 'true'
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install Hatch
        run: pip install hatch
      - name: Load cached venv
        id: cached-hatch-dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-embedchain-${{ runner.os }}-${{ hashFiles('**/pyproject.toml') }}
      - name: Install dependencies
        run: cd embedchain && make install_all
        if: steps.cached-hatch-dependencies.outputs.cache-hit != 'true'
      - name: Run Formatting
        run: |
          mkdir -p embedchain/.ruff_cache && chmod -R 777 embedchain/.ruff_cache
          cd embedchain && hatch run format
      - name: Lint with ruff
        run: cd embedchain && make lint
      - name: Run tests and generate coverage report
        run: cd embedchain && make coverage
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: coverage.xml
        env:
          CODECOV_TOKEN: ${{ secrets.CODECOV_TOKEN }}