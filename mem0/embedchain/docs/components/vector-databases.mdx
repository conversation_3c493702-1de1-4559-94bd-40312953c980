---
title: 🗄️ Vector databases
---

## Overview

Utilizing a vector database alongside Embedchain is a seamless process. All you need to do is configure it within the YAML configuration file. We've provided examples for each supported database below:

<CardGroup cols={4}>
  <Card title="ChromaDB" href="#chromadb"></Card>
  <Card title="Elasticsearch" href="#elasticsearch"></Card>
  <Card title="OpenSearch" href="#opensearch"></Card>
  <Card title="Zilliz" href="#zilliz"></Card>
  <Card title="LanceDB" href="#lancedb"></Card>
  <Card title="Pinecone" href="#pinecone"></Card>
  <Card title="Qdrant" href="#qdrant"></Card>
  <Card title="Weaviate" href="#weaviate"></Card>
</CardGroup>

<Snippet file="missing-vector-db-tip.mdx" />
