---
title: LanceDB
---

## Install Embedchain with LanceDB

Install Embedchain, LanceDB and  related dependencies using the following command:

```bash
pip install "embedchain[lancedb]"
```

LanceDB is a developer-friendly, open source database for AI. From hyper scalable vector search and advanced retrieval for RAG, to streaming training data and interactive exploration of large scale AI datasets.
In order to use LanceDB as vector database, not need to set any key for local use. 

### With OPENAI 
<CodeGroup>

```python main.py
import os
from embedchain import App

# set OPENAI_API_KEY as env variable
os.environ["OPENAI_API_KEY"] = "sk-xxx"

# create Embedchain App and set config
app = App.from_config(config={
    "vectordb": {
        "provider": "lancedb",
            "config": {
                "collection_name": "lancedb-index"
            }
        }
    }
)

# add data source and start query in
app.add("https://www.forbes.com/profile/elon-musk")

# query continuously
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)
```

</CodeGroup>

### With Local LLM 
<CodeGroup>

```python main.py
from embedchain import Pipeline as App

# config for Embedchain App
config = {
  'llm': {
    'provider': 'huggingface',
    'config': {
      'model': 'mistralai/Mistral-7B-v0.1',
      'temperature': 0.1,
      'max_tokens': 250,
      'top_p': 0.1,
      'stream': True
    }
  },
  'embedder': {
    'provider': 'huggingface',
    'config': {
      'model': 'sentence-transformers/all-mpnet-base-v2'
    }
  },
  'vectordb': { 
    'provider': 'lancedb', 
    'config': { 
      'collection_name': 'lancedb-index' 
    } 
  }
}

app = App.from_config(config=config)

# add data source and start query in
app.add("https://www.tesla.com/ns_videos/2022-tesla-impact-report.pdf")

# query continuously
while(True):
    question = input("Enter question: ")
    if question in ['q', 'exit', 'quit']:
        break
    answer = app.query(question)
    print(answer)
```

</CodeGroup>


<Snippet file="missing-vector-db-tip.mdx" />