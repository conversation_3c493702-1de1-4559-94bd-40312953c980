---
title: '📁 Directory/Folder'
---

To use an entire directory as data source, just add `data_type` as `directory` and pass in the path of the local directory.

### Without customization

```python
import os
from embedchain import App

os.environ["OPENAI_API_KEY"] = "sk-xxx"

app = App()
app.add("./elon-musk", data_type="directory")
response = app.query("list all files")
print(response)
# Answer: Files are elon-musk-1.txt, elon-musk-2.pdf.
```

### Customization

```python
import os
from embedchain import App
from embedchain.loaders.directory_loader import DirectoryLoader

os.environ["OPENAI_API_KEY"] = "sk-xxx"
lconfig = {
    "recursive": True,
    "extensions": [".txt"]
}
loader = DirectoryLoader(config=lconfig)
app = App()
app.add("./elon-musk", loader=loader)
response = app.query("what are all the files related to?")
print(response)

# Answer: The files are related to <PERSON>on <PERSON>sk.
```
