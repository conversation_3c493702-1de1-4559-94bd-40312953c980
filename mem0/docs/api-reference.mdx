---
title: Overview
icon: "info"
iconType: "solid"
---

<Snippet file="paper-release.mdx" />

Mem0 provides a powerful set of APIs that allow you to integrate advanced memory management capabilities into your applications. Our APIs are designed to be intuitive, efficient, and scalable, enabling you to create, retrieve, update, and delete memories across various entities such as users, agents, apps, and runs.

## Key Features

- **Memory Management**: Add, retrieve, update, and delete memories with ease.
- **Entity-based Operations**: Perform operations on memories associated with specific users, agents, apps, or runs.
- **Advanced Search**: Utilize our search API to find relevant memories based on various criteria.
- **History Tracking**: Access the history of memory interactions for comprehensive analysis.
- **User Management**: Manage user entities and their associated memories.

## API Structure

Our API is organized into several main categories:

1. **Memory APIs**: Core operations for managing individual memories and collections.
2. **Entities APIs**: Manage different entity types (users, agents, etc.) and their associated memories.
3. **Search API**: Advanced search functionality to retrieve relevant memories.
4. **History API**: Track and retrieve the history of memory interactions.

## Authentication

All API requests require authentication using HTTP Basic Auth. Ensure you include your API key in the Authorization header of each request.

## Organizations and projects (optional)

Organizations and projects provide the following capabilities:

- **Multi-org/project Support**: Specify organization and project when initializing the Mem0 client to attribute API usage appropriately
- **Member Management**: Control access to data through organization and project membership
- **Access Control**: Only members can access memories and data within their organization/project scope
- **Team Isolation**: Maintain data separation between different teams and projects for secure collaboration

Example with the mem0 Python package:

<Tabs>
  <Tab title="Python">

```python
from mem0 import MemoryClient
client = MemoryClient(org_id='YOUR_ORG_ID', project_id='YOUR_PROJECT_ID')
```

  </Tab>

  <Tab title="Node.js">

```javascript
import { MemoryClient } from "mem0ai";
const client = new MemoryClient({organizationId: "YOUR_ORG_ID", projectId: "YOUR_PROJECT_ID"});
```

  </Tab>
</Tabs>

## Getting Started

To begin using the Mem0 API, you'll need to:

1. Sign up for a [Mem0 account](https://app.mem0.ai) and obtain your API key.
2. Familiarize yourself with the API endpoints and their functionalities.
3. Make your first API call to add or retrieve a memory.

Explore the detailed documentation for each API endpoint to learn more about request/response formats, parameters, and example usage.
