import asyncio
from memory_manager import MemoryManager
from mem0.memory.main import Memory
from config.config import config


class Mem0Manager(MemoryManager):
    
    def _init_memory_client(self):
        return Memory.from_config(config)

    async def _add_memory(self, message, user_id, metadata):
        return await asyncio.to_thread(self.memory.add, message, user_id=user_id, metadata=metadata)

    async def _search_memory(self, user_id, query):
        memories = await asyncio.to_thread(self.memory.search, query, user_id=user_id)
        
        if not self.is_graph:
            semantic_memories = [
                {
                    "memory": memory["memory"],
                    "timestamp": memory["metadata"]["timestamp"],
                    "score": round(memory["score"], 2),
                }
                for memory in memories["results"]
            ]
            graph_memories = None
        else:
            semantic_memories = [
                {
                    "memory": memory["memory"],
                    "timestamp": memory["metadata"]["timestamp"],
                    "score": round(memory["score"], 2),
                }
                for memory in memories["results"]
            ]
            graph_memories = [
                {"source": relation["source"], "relationship": relation["relationship"],
                    "target": relation["destination"]}
                for relation in memories["relations"]
            ]
        return semantic_memories, graph_memories
    
    async def _delete_memory(self, user_id):
        await asyncio.to_thread(self.memory.delete_all, user_id=user_id)