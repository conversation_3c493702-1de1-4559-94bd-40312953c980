config = {
    # "llm": {
    #     "provider": "openai",
    #     "config": {
    #         "model": "models/gemini-2.0-flash-lite",
    #         "temperature": 0.1,
    #         "api_key": "AIzaSyAvJAMyULpUYUWknX9dH8cxlCSt_EYi8d0",
    #         "openai_base_url": "https://www.xiaohei.tech"
    #     }
    # },
    "llm": {
        "provider": "openai",
        "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1,
                    "api_key": "21897964815046574094",
                    "openai_base_url": "https://aigc.sankuai.com/v1/openai/native",
                    "max_tokens": 16384
        }
    },
    "embedder": {
        "provider": "openai",
        "config": {
            "model": "models/text-embedding-004",
            "embedding_dims": 768,
            "api_key": "AIzaSyAvJAMyULpUYUWknX9dH8cxlCSt_EYi8d0",
            "openai_base_url": "https://www.xiaohei.tech"
        }
    },
    "vector_store": {
        "provider": "qdrant",
        "config": {
            "embedding_model_dims": 768,
            "collection_name": "test",
            "path": "/Users/<USER>/Documents/doing/code/opensource/memory/mymem/qdrant_storage",
            "on_disk": True,
        }
    },
    "graph_store": {
        "provider": "neo4j",
        "config": {
            "url": "neo4j://127.0.0.1:7687",
            "username": "neo4j",
            "password": "password"
        },
        "llm": {
            "provider": "openai",
            "config": {
                "model": "gpt-4o-mini",
                "temperature": 0.0,
                "api_key": "21897964815046574094",
                "openai_base_url": "https://aigc.sankuai.com/v1/openai/native",
                "max_tokens": 16384
            }
        }
    }
}

llm_config = config['llm']['config']
JUDGE_MODEL = llm_config['model']
