import asyncio
from datetime import datetime, timezone
from dateutil.parser import parse

from memory_manager import MemoryManager
from config.config import config

# Import Graphiti components
from graphiti_core import Graphiti
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.cross_encoder.openai_reranker_client import OpenAIRerankerClient
from graphiti_core.nodes import EpisodeType


class GraphitiManager(MemoryManager):

    def _init_memory_client(self):
        """Initialize the Graphiti client with configuration from config.py"""
        llm_config_data = config['llm']['config']
        embedder_config_data = config['embedder']['config']
        graph_store_config = config['graph_store']['config']

        llm_config = LLMConfig(
            api_key=llm_config_data['api_key'], model=llm_config_data['model'],
            small_model=llm_config_data.get('model', llm_config_data['model']),
            base_url=llm_config_data['openai_base_url'],
            temperature=llm_config_data.get('temperature', 0.1)
        )
        llm_client = OpenAIClient(config=llm_config)

        embedder_config = OpenAIEmbedderConfig(
            api_key=embedder_config_data['api_key'],
            embedding_model=embedder_config_data['model'],
            embedding_dim=embedder_config_data['embedding_dims'],
            base_url=embedder_config_data['openai_base_url']
        )
        embedder = OpenAIEmbedder(config=embedder_config)

        cross_encoder = OpenAIRerankerClient(
            config=llm_config, client=llm_client)

        neo4j_uri = graph_store_config['url'].replace('neo4j://', 'bolt://')
        neo4j_user = graph_store_config['username']
        neo4j_password = graph_store_config['password']

        graphiti = Graphiti(
            neo4j_uri, neo4j_user, neo4j_password,
            llm_client=llm_client, embedder=embedder, cross_encoder=cross_encoder
        )

        self._indices_built = False
        return graphiti

    async def _ensure_indices(self):
        if not self._indices_built:
            await self.memory.build_indices_and_constraints()
            self._indices_built = True

    async def _add_memory(self, message, user_id, metadata):
        await self._ensure_indices()
        try:
            if isinstance(message, list):
                if len(message) > 100:
                    message_batch = [message[i:i + 100]
                                     for i in range(0, len(message), 100)]
                    return await self._add_memory_bulk(message_batch, user_id, metadata)

                episode_content = ""
                episode_type = EpisodeType.message
                for msg in message:
                    role = msg.get('role', 'user')
                    content = msg.get('content', str(msg))
                    speaker = user_id if role == 'user' else 'assistant'
                    episode_content += f"{speaker}: {content}\n"
                episode_content = episode_content.rstrip('\n')
            else:
                episode_content = str(message)
                episode_type = EpisodeType.text

            timestamp_str = metadata.get('timestamp')
            if timestamp_str:
                reference_time = parse(timestamp_str)
            else:
                reference_time = datetime.now(timezone.utc)

            result = await self.memory.add_episode(
                name=f"Conversation_{user_id}_{reference_time.strftime('%Y%m%d_%H%M%S')}",
                episode_body=episode_content, source=episode_type,
                source_description=f"User {user_id} conversation memory",
                reference_time=reference_time, group_id=f"user_{user_id}"
            )
            return {"status": "success", "episode_id": str(result) if result else None}
        except Exception as e:
            print(f"Error adding memory to Graphiti: {e}")
            raise

    async def _add_memory_bulk(self, messages_batch, user_id, metadata):
        await self._ensure_indices()
        try:
            from graphiti_core.utils.bulk_utils import RawEpisode
            bulk_episodes = []
            user_group_id = f"user_{user_id}"

            for i, message in enumerate(messages_batch):
                if isinstance(message, list):
                    episode_content = ""
                    episode_type = EpisodeType.message
                    for msg in message:
                        role = msg.get('role', 'user')
                        content = msg.get('content', str(msg))
                        speaker = user_id if role == 'user' else 'assistant'
                        episode_content += f"{speaker}: {content}\n"
                    episode_content = episode_content.rstrip('\n')
                else:
                    episode_content = str(message)
                    episode_type = EpisodeType.text

                timestamp_str = metadata.get('timestamp')
                if timestamp_str:
                    reference_time = parse(timestamp_str)
                else:
                    reference_time = datetime.now(timezone.utc)

                episode = RawEpisode(
                    name=f"Bulk_Conversation_{user_id}_{i}_{reference_time.strftime('%Y%m%d_%H%M%S')}",
                    content=episode_content, source=episode_type,
                    source_description=f"User {user_id} bulk conversation memory",
                    reference_time=reference_time, group_id=user_group_id
                )
                bulk_episodes.append(episode)

            await self.memory.add_episode_bulk(bulk_episodes)
            return {"status": "success", "episodes_added": len(bulk_episodes)}
        except Exception as e:
            print(f"Error adding bulk memories to Graphiti: {e}")
            raise

    async def _search_memory(self, user_id, query):
        await self._ensure_indices()
        try:
            user_group_id = f"user_{user_id}"
            user_node_uuid = None
            try:
                from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
                user_node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(
                    deep=True)
                user_node_search_config.limit = 5
                user_node_search_results = await self.memory.search_(
                    query=user_id, group_ids=[
                        user_group_id], config=user_node_search_config
                )
                if user_node_search_results.nodes:
                    user_node_uuid = user_node_search_results.nodes[0].uuid
            except Exception as e:
                print(f"Could not find user node for {user_id}: {e}")

            if user_node_uuid:
                search_results = await self.memory.search(
                    query, center_node_uuid=user_node_uuid, group_ids=[
                        user_group_id], num_results=10
                )
            else:
                search_results = await self.memory.search(
                    query, group_ids=[user_group_id], num_results=10
                )

            semantic_memories, graph_memories = [], []
            uuid_to_name_map = {}
            if self.is_graph and search_results:
                node_uuids = set()
                for r in search_results:
                    if hasattr(r, 'source_node_uuid'):
                        node_uuids.add(r.source_node_uuid)
                    if hasattr(r, 'target_node_uuid'):
                        node_uuids.add(r.target_node_uuid)
                
                if node_uuids:
                    try:
                        query = "MATCH (n:Entity) WHERE n.uuid IN $uuids RETURN n.uuid as uuid, n.name as name"
                        records, _, _ = await self.memory.driver.execute_query(query, uuids=list(node_uuids))
                        uuid_to_name_map = {record["uuid"]: record["name"] for record in records}
                    except Exception as e:
                        print(f"Could not get node names for graph memories, falling back to UUIDs: {e}")

            for r in search_results:
                semantic_memories.append({"memory": r.fact, "timestamp": r.created_at.isoformat(
                ), "score": r.score if hasattr(r, 'score') else 1})
                if self.is_graph and hasattr(r, 'source_node_uuid'):
                    source_name = uuid_to_name_map.get(r.source_node_uuid, r.source_node_uuid)
                    target_name = uuid_to_name_map.get(r.target_node_uuid, r.target_node_uuid)
                    graph_memories.append(
                        {"source": source_name, "relationship": r.fact, "target": target_name})

            return semantic_memories, graph_memories if self.is_graph else None
        except Exception as e:
            print(f"Error searching memory in Graphiti: {e}")
            return ([], None) if not self.is_graph else ([], [])

    async def build_communities(self, group_id=None):
        await self._ensure_indices()
        try:
            if group_id:
                await self.memory.build_communities(group_ids=[group_id])
            else:
                await self.memory.build_communities()
            print(f"Communities built successfully")
            return {"status": "success", "communities_built": True}
        except Exception as e:
            print(f"Error building communities: {e}")
            return {"status": "error", "error": str(e)}

    async def get_user_communities(self, user_id):
        await self._ensure_indices()
        try:
            user_group_id = f"user_{user_id}"
            from graphiti_core.search.search_config_recipes import COMMUNITY_HYBRID_SEARCH_RRF
            community_search_config = COMMUNITY_HYBRID_SEARCH_RRF.model_copy(
                deep=True)
            community_search_config.limit = 5
            search_results = await self.memory.search_(
                query=f"user {user_id}", group_ids=[user_group_id], config=community_search_config
            )
            communities = []
            for community in search_results.communities:
                communities.append({
                    "uuid": community.uuid, "name": community.name,
                    "summary": community.summary[:200] + "..." if len(community.summary) > 200 else community.summary
                })
            return communities
        except Exception as e:
            print(f"Error getting user communities: {e}")
            return []

    async def _delete_memory(self, user_id):
        await self._ensure_indices()
        try:
            user_group_id = f"user_{user_id}"
            print(
                f"Starting deletion process for user: {user_id} (group_id: {user_group_id})")

            all_node_uuids = set()
            try:
                search_results = await self.memory.search(query=user_id, group_ids=[user_group_id], num_results=1000)
                for r in search_results:
                    if hasattr(r, 'source_node_uuid'):
                        all_node_uuids.add(r.source_node_uuid)
                    if hasattr(r, 'target_node_uuid'):
                        all_node_uuids.add(r.target_node_uuid)
                print(
                    f"Found {len(search_results)} search results via search method")
            except Exception as e:
                print(f"Search method failed: {e}")

            try:
                from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
                node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(
                    deep=True)
                node_search_config.limit = 1000
                node_search_results = await self.memory.search_(
                    query=user_id, group_ids=[
                        user_group_id], config=node_search_config
                )
                for node in node_search_results.nodes:
                    all_node_uuids.add(node.uuid)
                print(
                    f"Found {len(node_search_results.nodes)} additional nodes via search_")
            except Exception as e:
                print(f"Node search via search_ failed: {e}")

            if not all_node_uuids:
                print(f"No nodes found for user: {user_id}")
                return {
                    "status": "success",
                    "user_id": user_id,
                    "group_id": user_group_id,
                    "deleted_count": 0,
                    "method": "graphiti_search_batch_delete",
                    "nodes_found": 0
                }

            node_uuid_list = list(all_node_uuids)
            query_str = "MATCH (n) WHERE n.uuid IN $node_uuids DETACH DELETE n RETURN count(n) as deleted_count"
            result = await self.memory.driver.execute_query(query_str, node_uuids=node_uuid_list)
            deleted_count = result.records[0]["deleted_count"] if result.records else 0
            print(
                f"Successfully deleted {deleted_count} nodes for user: {user_id}")
            return {
                "status": "success",
                "user_id": user_id,
                "group_id": user_group_id,
                "deleted_count": deleted_count,
                "method": "graphiti_search_batch_delete",
                "nodes_found": len(all_node_uuids)
            }
        except Exception as e:
            print(f"Error deleting memories for user {user_id}: {e}")
            return {"status": "error", "error": str(e)}
