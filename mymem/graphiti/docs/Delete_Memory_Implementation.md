# Graphiti _delete_memory Method Implementation

## Overview

This document describes the implementation challenges and solutions for the `_delete_memory` method in GraphitiManager, addressing the limitations of Graphiti's current API.

## 🚨 Implementation Challenges

### 1. **Lack of High-Level Deletion API**

**Problem**: Graphiti doesn't provide a built-in method to delete all data for a specific `group_id`. The available options are:

- `node.delete(driver)` - Deletes individual nodes by UUID (requires knowing specific UUIDs)
- `clear_data(driver)` - Deletes the entire database (too destructive)
- No group-level or user-level deletion methods

### 2. **CRUD Documentation Limitations**

The CRUD documentation only shows:
```python
# Individual node deletion - requires specific UUID
async def delete(self, driver: AsyncDriver):
    result = await driver.execute_query(
        "MATCH (n:Entity {uuid: $uuid}) DETACH DELETE n",
        uuid=self.uuid,
    )
```

This doesn't help with deleting all nodes for a user/group.

### 3. **Search API Limitations**

While we can search by `group_id`:
```python
search_results = await graphiti.search(query="*", group_id="user_123")
```

The search results don't provide direct access to node UUIDs or deletion methods.

## 🔧 Implementation Approach

### Current Solution: Graphiti Search + Neo4j Batch Deletion

We now use a two-step approach that combines Graphiti's high-level APIs with efficient batch deletion:

```python
def _delete_memory(self, user_id):
    """Delete all memories for a specific user using Graphiti search + Neo4j batch deletion"""
    user_group_id = f"user_{user_id}"

    # Step 1: Use Graphiti's search APIs to find all nodes
    all_node_uuids = set()

    # Method 1: Use get_nodes_by_query
    user_nodes = await self.memory.get_nodes_by_query(
        query="", group_id=user_group_id
    )
    for node in user_nodes:
        all_node_uuids.add(node.uuid)

    # Method 2: Use _search with NODE_HYBRID_SEARCH_RRF
    from graphiti_core.search.search_config_recipes import NODE_HYBRID_SEARCH_RRF
    node_search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
    node_search_config.limit = 1000

    node_search_results = await self.memory._search(
        query="", group_id=user_group_id, config=node_search_config
    )
    for node in node_search_results.nodes:
        all_node_uuids.add(node.uuid)

    # Step 2: Batch delete using collected UUIDs
    if all_node_uuids:
        batch_delete_query = """
        MATCH (n)
        WHERE n.uuid IN $node_uuids
        DETACH DELETE n
        RETURN count(n) as deleted_count
        """

        result = await self.memory.driver.execute_query(
            batch_delete_query,
            node_uuids=list(all_node_uuids)
        )
```

### Why This Approach?

1. **Uses Official APIs**: Leverages Graphiti's `get_nodes_by_query` and `_search` methods
2. **Comprehensive Discovery**: Uses multiple search methods to find all nodes
3. **Efficient Deletion**: Batch deletion is much faster than individual deletions
4. **Safe Parameters**: Uses parameterized queries with collected UUIDs
5. **Complete Coverage**: Combines different search strategies to catch all nodes

## Usage Examples

### Basic Usage
```python
from graphiti.manager import GraphitiManager

manager = GraphitiManager(is_graph=True)

# Add some memories
manager._add_memory("John likes basketball", "user123", {"timestamp": "2024-01-01T10:00:00Z"})

# Delete all memories for the user
result = manager._delete_memory("user123")
print(result)  # {"status": "success", "user_id": "user123", "group_id": "user_user123", ...}

# Clean up
manager.close_all_connections()
```

### Error Handling
```python
# Delete non-existent user (safe operation)
result = manager._delete_memory("non_existent_user")
if result["status"] == "success":
    print("Deletion completed")
else:
    print(f"Error: {result['error']}")
```

## Integration with Existing Codebase

### Consistency with Other Methods
- Uses the same `group_id` pattern as `_add_memory` and `_search_memory`
- Follows the same async execution pattern
- Uses the same error handling approach
- Maintains the same logging style

### Memory Manager Interface
The method implements the abstract `_delete_memory` method from the `MemoryManager` base class, ensuring compatibility with the existing framework.

## Additional Features

### close_all_connections Method
Also implemented a `close_all_connections` method for proper resource cleanup:

```python
def close_all_connections(self):
    """Close all Graphiti connections and clean up resources"""
    # Closes Neo4j driver and resets initialization flag
```

## Testing

Two test files are provided:

1. **test_graphiti_delete.py**: Comprehensive test of the delete functionality
2. **demo_graphiti_delete.py**: Demonstration of user isolation and selective deletion

Run tests with:
```bash
python test_graphiti_delete.py
python demo_graphiti_delete.py
```

## Safety Considerations

1. **User Isolation**: Only deletes data for the specified user (group_id)
2. **No Cross-User Impact**: Other users' data remains untouched
3. **Complete Cleanup**: Removes both nodes and relationships
4. **Error Recovery**: Graceful error handling prevents system crashes

## Compliance with Graphiti Best Practices

- Uses official `group_id` approach for user isolation
- Follows CRUD documentation patterns
- Uses `DETACH DELETE` as recommended
- Maintains consistency with Graphiti's design principles
