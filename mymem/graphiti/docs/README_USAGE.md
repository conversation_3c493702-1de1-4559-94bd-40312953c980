# GraphitiManager 使用说明

## 概述

GraphitiManager 是基于 Graphiti 知识图谱框架实现的记忆管理器，参考了 memzero/manager.py 的设计模式，使用 config/config.py 中的配置信息。

## 功能特性

- **知识图谱存储**: 使用 Graphiti 将对话内容转换为知识图谱
- **语义搜索**: 支持语义相似度和关键词搜索
- **图谱关系**: 提取实体间的关系信息
- **配置兼容**: 使用项目统一的配置文件

## 安装依赖

```bash
# 安装 Graphiti 依赖
python install_graphiti_deps.py

# 或者手动安装
pip install graphiti-core python-dotenv
```

## 配置要求

确保 `config/config.py` 中包含以下配置：

```python
config = {
    "llm": {
        "provider": "openai",
        "config": {
            "model": "models/gemini-2.0-flash-lite",
            "temperature": 0.1,
            "api_key": "your-api-key",
            "openai_base_url": "your-base-url"
        }
    },
    "embedder": {
        "provider": "openai", 
        "config": {
            "model": "models/text-embedding-004",
            "embedding_dims": 768,
            "api_key": "your-api-key",
            "openai_base_url": "your-base-url"
        }
    },
    "graph_store": {
        "provider": "neo4j",
        "config": {
            "url": "neo4j://127.0.0.1:7687",
            "username": "neo4j", 
            "password": "password"
        }
    }
}
```

## 使用方法

### 1. 通过 run_experiments.py 使用

```bash
python run_experiments.py --technique_type grphiti --is_graph
```

### 2. 直接使用 GraphitiManager

```python
from graphiti.manager import GraphitiManager

# 初始化管理器
manager = GraphitiManager(is_graph=True)

# 添加记忆
user_id = "user_123"
message = "John likes to play basketball on weekends."
metadata = {"timestamp": "2024-01-01T10:00:00Z"}

manager._add_memory(message, user_id, metadata)

# 搜索记忆
query = "What does John like to do?"
semantic_memories, graph_memories = manager._search_memory(user_id, query)

# 关闭连接
manager.close_all_connections()
```

### 3. 测试功能

```bash
python test_graphiti.py
```

## 主要方法

### `_init_memory_client()`
- 初始化 Graphiti 客户端
- 使用配置文件中的 LLM、嵌入模型和 Neo4j 设置

### `_add_memory(message, user_id, metadata)`
- 添加记忆到知识图谱
- 支持文本和消息列表格式
- 自动提取实体和关系

### `_search_memory(user_id, query)`
- 搜索用户记忆
- 返回语义记忆和图谱关系
- 支持混合搜索（语义+关键词）

### `close_all_connections()`
- 关闭所有 Graphiti 连接
- 清理资源

## 注意事项

1. **Neo4j 数据库**: 确保 Neo4j 数据库正在运行
2. **API 密钥**: 配置正确的 LLM 和嵌入模型 API 密钥
3. **异步操作**: 内部使用异步操作，自动处理事件循环
4. **用户隔离**: 每个用户使用独立的 Graphiti 实例
5. **资源清理**: 使用完毕后调用 `close_all_connections()`

## 故障排除

### 常见问题

1. **连接错误**: 检查 Neo4j 是否运行，配置是否正确
2. **API 错误**: 验证 API 密钥和端点配置
3. **依赖问题**: 确保安装了 graphiti-core
4. **内存问题**: 大量数据时注意资源管理

### 调试模式

在代码中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 与 Mem0Manager 的对比

| 特性 | Mem0Manager | GraphitiManager |
|------|-------------|-----------------|
| 存储方式 | 向量数据库 | 知识图谱 |
| 关系提取 | 基础 | 高级 |
| 时间感知 | 有限 | 强大 |
| 查询能力 | 语义搜索 | 语义+图谱搜索 |
| 复杂度 | 简单 | 中等 |
