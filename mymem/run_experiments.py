import argparse
import async<PERSON>
import os

from memzero.manager import Mem<PERSON><PERSON>ana<PERSON>
from graphiti.manager import <PERSON><PERSON><PERSON>iManager
from utils import TECHNIQUES


async def main():
    parser = argparse.ArgumentParser(description="Run memory experiments")
    parser.add_argument("--input_dataset", type=str,
                        default="dataset/locomo10.json", help="Input path for dataset")
    parser.add_argument("--technique_type", choices=TECHNIQUES,
                        default="mem0", help="Memory technique to use")
    parser.add_argument("--chunk_size", type=int,
                        default=1000, help="Chunk size for processing")
    parser.add_argument("--output_folder", type=str,
                        default="results/", help="Output path for results")
    parser.add_argument("--is_graph", action="store_true",
                        default=False, help="Whether to use graph-based search")
    parser.add_argument("--num_chunks", type=int, default=1,
                        help="Number of chunks to process")

    args = parser.parse_args()

    print(
        f"Running experiments with technique: {args.technique_type}, chunk size: {args.chunk_size}")

    if args.technique_type == "mem0":
        output_file_path = os.path.join(
            args.output_folder,
            f"mem0_results_graph_{args.is_graph}.json",
        )
        manager = Mem0Manager(data_path=args.input_dataset, is_graph=args.is_graph,
                                   output_path=output_file_path)
    elif args.technique_type == "graphiti":
        output_file_path = os.path.join(
            args.output_folder,
            f"graphiti_results_graph_{args.is_graph}.json",
        )
        manager = GraphitiManager(data_path=args.input_dataset, is_graph=args.is_graph,
                                          output_path=output_file_path)
    else:
        raise ValueError(f"Invalid technique type: {args.technique_type}")

    # await manager.process_all_conversations()
    await manager.process_data_file(args.input_dataset)


if __name__ == "__main__":
    asyncio.run(main())
